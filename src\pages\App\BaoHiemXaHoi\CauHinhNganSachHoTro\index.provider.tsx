import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {defaultPaginationTableProps} from "@src/hooks";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";
import {CauHinhNganSachHoTroContext} from "./index.context";
import {ICauHinhNganSachHoTroContextProps} from "./index.model";

const CauHinhNganSachHoTroProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachNganSachHoTroNgayApDung, setDanhSachNganSachHoTroNgayApDung] = useState<Array<CommonExecute.Execute.INganSachHoTroNgayApDung>>([]);
  const [listTinhThanh, setListTinhThanh] = useState<Array<CommonExecute.Execute.IDanhMucTinhThanh>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [chiTietTinhThanh, setChiTietTinhThanh] = useState<CommonExecute.Execute.IDanhMucTinhThanh>({} as CommonExecute.Execute.IDanhMucTinhThanh);
  const [chiTietNganSachHoTro, setChiTietNganSachHoTro] = useState<CommonExecute.Execute.INganSachHoTro>({} as CommonExecute.Execute.INganSachHoTro);
  const [listDonViThuHo, setListDonViThuHo] = useState<Array<CommonExecute.Execute.IDonViThuHo>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhMucTinhThanhParams & ReactQuery.IPhanTrang>({
    ngay_ad: undefined,
    ma: "",
    ten: "",
    trang_thai: "",
    trang: 1,
    so_dong: defaultPaginationTableProps.defaultPageSize,
  });
  const [ngayAdMoiTao, setNgayAdMoiTao] = useState<string | null>(null);
  //khởi tạo dữ liệu ban đầu
  useEffect(() => {
    initData();
  }, []);
  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
  const initData = () => {
    getListDonViThuHo();
    // Khởi tạo dữ liệu ban đầu khi component mount (hiện tại không cần load gì)
  };
  useEffect(() => {
    getListTinhThanh();
  }, [filterParams]);

  //danh sách đươn vị thu hộ liệt kê
  const getListDonViThuHo = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DON_VI_THU_HO_BHXH,
      } as any);
      console.log("response.data", response.data);
      if (response.data) {
        setListDonViThuHo(response.data);
      }
    } catch (error) {
      console.log("getListDonViThuHo error ", error);
    }
  }, [mutateUseCommonExecute]);
  const getListTinhThanh = useCallback(async () => {
    // Tìm kiếm và load danh sách tỉnh thành theo filter parameters
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DANH_MUC_TINH_THANH,
      } as any);

      // Response structure confirmed: data.data[] + data.tong_so_dong
      const responseData = response.data;
      if (responseData?.data && Array.isArray(responseData.data)) {
        setListTinhThanh(responseData.data);
        setTongSoDong(responseData.tong_so_dong || responseData.data.length);
      }
    } catch (error) {
      message.error("Không thể tải danh sách tỉnh thành");
    }
  }, [mutateUseCommonExecute, filterParams]);

  const layChiTietDanhMucTinhThanh = useCallback(
    async (params: ReactQuery.IChiTietDanhMucTinhThanhParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.CHI_TIET_DANH_MUC_TINH_THANH,
        } as any);
        // Xử lý structure response.data.lke
        const responseData = response.data as any; // Type casting để truy cập lke
        if (responseData && responseData.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          const result = responseData.lke[0] as CommonExecute.Execute.IDanhMucTinhThanh;
          setChiTietTinhThanh(result);
          console.log("chiTietTinhThanh", result);
          return result;
        } else {
          console.log("Không tìm thấy data trong response.data.lke");
          return {} as CommonExecute.Execute.IDanhMucTinhThanh;
        }
      } catch (error) {
        console.log("layChiTietTinh error", error);
        return {} as CommonExecute.Execute.IDanhMucTinhThanh;
      }
    },
    [mutateUseCommonExecute],
  );
  const layDanhSachNganSachHoTroNgayApDung = useCallback(
    async (params: ReactQuery.ILietKeNganSachHoTroNgayApDungParams) => {
      try {
        console.log("params", params);
        const response = await mutateUseCommonExecute.mutateAsync({
          ma_tinh: params.ma_tinh,
          ngay_ad: params.ngay_ad,
          actionCode: ACTION_CODE.LIET_KE_NGAN_SACH_HO_TRO_NGAY_AD,
        });
        console.log("response.data", response.data);
        setDanhSachNganSachHoTroNgayApDung(response.data);
        return response.data as CommonExecute.Execute.INganSachHoTroNgayApDung;
      } catch (error) {
        console.log("layDanhSachNganSachNgayApDung error", error);
        return {} as CommonExecute.Execute.INganSachHoTroNgayApDung;
      }
    },
    [mutateUseCommonExecute],
  );
  //cập nhật ngân sách hỗ trợ ngày áp dụng
  const updateNganSachHoTroNgayApDung = useCallback(
    async (body: ReactQuery.IUpdateNganSachHoTroNgayApDungParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_NGAN_SACH_HO_TRO_NGAY_AD,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          return true;
        } else return false;
      } catch (error: any) {
        console.log("updateNganSachHoTroNgayApDung error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );
  //chi tiết ngân sách hỗ trợ
  const layChiTietNganSachHoTro = useCallback(
    async (params: ReactQuery.IChiTietNganSachHoTroParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.CHI_TIET_NGAN_SACH_HO_TRO,
        });
        console.log("response.data", response.data);
        console.log("response.data.lke", (response.data as any).lke);
        // Sử dụng response.data.lke vì đó là dữ liệu thực tế được return
        const chiTietData = (response.data as any).lke || response.data;
        setChiTietNganSachHoTro(chiTietData as CommonExecute.Execute.INganSachHoTro);
        return chiTietData as CommonExecute.Execute.INganSachHoTro;
      } catch (error) {
        console.log("layChiTietNganSachHoTro error", error);
        return {} as CommonExecute.Execute.INganSachHoTro;
      }
    },
    [mutateUseCommonExecute],
  );
  const CapNhatNganSachHoTro = useCallback(
    async (body: ReactQuery.ICapNhatNganSachHoTroParams) => {
      try {
        const params = {
          ...body,

          actionCode: ACTION_CODE.CAP_NHAT_NGAN_SACH_HO_TRO,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          return true;
        } else return false;
      } catch (error: any) {
        console.log("CapNhatNganSachHoTro error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );
  //xóa ngày áp dụng ngân sách hỗ trợ
  const xoaNgayApDungNganSachHoTro = useCallback(
    async (body: ReactQuery.IUpdateNganSachHoTroNgayApDungParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.XOA_NGAN_SACH_HO_TRO_NGAY_AD,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Xóa ngày áp dụng thành công!");
          return true;
        } else return false;
      } catch (error: any) {
        console.log("xoaNgayApDungNganSachHoTro error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  const value = useMemo<ICauHinhNganSachHoTroContextProps>(
    () => ({
      listTinhThanh,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,
      chiTietTinhThanh,
      danhSachNganSachHoTroNgayApDung,
      chiTietNganSachHoTro,
      listDonViThuHo,
      ngayAdMoiTao,
      setNgayAdMoiTao,
      CapNhatNganSachHoTro,
      layChiTietNganSachHoTro,
      getListTinhThanh,
      layDanhSachNganSachHoTroNgayApDung,
      layChiTietDanhMucTinhThanh,
      setFilterParams,
      updateNganSachHoTroNgayApDung,
      xoaNgayApDungNganSachHoTro,
    }),
    [
      listTinhThanh,
      tongSoDong,
      mutateUseCommonExecute,
      filterParams,
      danhSachNganSachHoTroNgayApDung,
      chiTietTinhThanh,
      chiTietNganSachHoTro,
      listDonViThuHo,
      ngayAdMoiTao,
      setNgayAdMoiTao,
      CapNhatNganSachHoTro,
      layChiTietNganSachHoTro,
      layDanhSachNganSachHoTroNgayApDung,
      layChiTietDanhMucTinhThanh,
      getListTinhThanh,
      setFilterParams,
      updateNganSachHoTroNgayApDung,
      xoaNgayApDungNganSachHoTro,
    ],
  );

  return <CauHinhNganSachHoTroContext.Provider value={value}>{children}</CauHinhNganSachHoTroContext.Provider>;
};

export default CauHinhNganSachHoTroProvider;
/**
 * - Quản lý state toàn cục cho module quản lý tỉnh thành
 * - Cung cấp các method để thao tác với API tỉnh thành
 * - Xử lý logic nghiệp vụ: tìm kiếm, phân trang, CRUD
 * - Chia sẻ dữ liệu giữa các component con thông qua Context
 */
