declare namespace CommonExecute {
  namespace Execute {
    interface IMenuNguoiDungTheoNhom {
      ma?: string;
      ma_cha?: string;
      nhom?: string;
      sl_menu_con?: number;
      stt: number;
      ten?: string;
      url?: string;
    }
    interface IPhongBan {
      ma?: string;
      ten?: string;
      ma_chi_nhanh?: string;
      ma_doi_tac?: string;
      stt: number;
      sott: number;
      label?: string;
      value?: string;
      dthoai?: string;
      email?: string;
      ngay_cap_nhat?: string;
      ngay_tao?: string;
      nguoi_cap_nhat?: string;
      nguoi_tao?: string;
      trang_thai?: string;
      trang_thai_ten?: string;
    }
    // interface IChiTietPhongBan {
    //   dthoai?: string;
    //   email?: string;
    //   ma?: string;
    //   ma_chi_nhanh?: string;
    //   ma_doi_tac?: string;
    //   ngay_cap_nhat?: string;
    //   ngay_tao?: string;
    //   nguoi_cap_nhat?: string;
    //   nguoi_tao?: string;
    //   stt?: number;
    //   ten?: string;
    //   trang_thai?: string;
    //   trang_thai_ten?: string;
    // }
    interface IDoiTac {
      ma?: string;
      ten?: string;
      ten_tat?: string;
      mst?: string;
      dchi?: string;
      dthoai?: string;
      ten_e?: string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
      label?: string;
      value?: string;
    }
    interface IChiNhanh {
      ma_doi_tac?: string;
      doi_tac_ten_tat?: string;
      ma?: string;
      ten?: string;
      ten_tat?: string;
      mst?: string;
      dchi?: string;
      dthoai?: string;
      stt?: number;
      trang_thai?: string;
      email?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
      label?: string;
      value?: string;
    }
    interface IDanhSachPhongBanPhanTrang {
      dthoai?: string;
      email?: string;
      ma?: string;
      ma_chi_nhanh?: string;
      ma_doi_tac?: string;
      ngay_cap_nhat?: string;
      ngay_tao?: string;
      nguoi_cap_nhat?: string;
      nguoi_tao?: string;
      stt?: number;
      sott?: number;
      ten?: string;
      trang_thai?: string;
      trang_thai_ten?: string;
      chi_nhanh_ten_tat?: string;
      doi_tac_ten_tat?: string;
    }
    interface IDanhSachTaiKhoanNguoiDungPhanTrang {
      ten: string;
      ma_doi_tac: string;
      doi_tac_ten_tat?: string;
      ma_chi_nhanh: string;
      chi_nhanh_ten_tat?: string;
      phong: string;
      phong_ten: string;
      ma?: string;
      stt: number;
      sott: number;
      trang_thai: string;
      trang_thai_ten: string;
      dthoai: string;
      email: string;
      ngay_tao: string;
      nguoi_tao: string;
      ngay_cap_nhat: string;
      nguoi_cap_nhat: string;
      ngay_hl: string;
      ngay_kt: string;
      ten_chuc_danh?: string;
    }
    interface IChiTietNguoiSuDung {
      ten?: string;
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      doi_tac_ten_tat?: string;
      ma_chi_nhanh_ql?: string;
      chi_nhanh_ten_tat?: string;
      phong?: string;
      phong_ten?: string;
      ma?: string;
      stt?: number;
      sott?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      dthoai?: string;
      email?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      ngay_hl?: string;
      ngay_kt?: string;
      nsd_menu?: Array<{
        is_checked: string;
        ma?: string;
        ma_cha?: string;
        ma_doi_tac?: string;
        nd_tim?: string;
        nhom?: string;
        ten?: string;
        url?: string;
        stt?: number;
      }>;
      nsd_qly?: Array<{
        is_checked: string;
        ma?: string;
        ma_cha?: string;
        ma_chi_nhanh_ql?: string;
        ma_doi_tac_ql?: string;
        ten_chi_nhanh_ql?: string;
        ten_doi_tac_ql?: string;
        nd_tim?: string;
      }>;
      nsd_quyen?: Array<{
        is_checked: number;
        ma_chuc_nang?: string;
        hanh_dong?: string;
        nd_tim?: string;
        ten_chuc_nang?: string;
      }>;
    }
    interface IChiTietTaiKhoanNguoiDung {
      nsd?: object;
      nsd_menu?: object;
      nsd_qly?: object;
      nsd_quyen?: object;
    }
    interface IDanhMucPhongBan {
      ma?: string;
      ten?: string;
      ma_chi_nhanh?: string;
      ma_doi_tac?: string;
      label?: string;
      value?: string;
    }

    // Danh mục chức danh
    interface IDanhMucChucDanh {
      ma?: string;
      ten?: string;
      label?: string;
      value?: string;
      ma_chi_nhanh?: string;
      ma_doi_tac?: string;
    }

    // Danh sách hệ thống chức năng phân trang
    interface IDanhSachHeThongChucNangPhanTrang {
      ten: string;
      ma_doi_tac: string;
      doi_tac_ten_tat?: string;
      ma_chi_nhanh: string;
      chi_nhanh_ten_tat?: string;
      phong: string;
      phong_ten: string;
      ma?: string;
      stt: number;
      sott: number;
      trang_thai: string;
      trang_thai_ten: string;
      dthoai: string;
      email: string;
      ngay_tao: string;
      nguoi_tao: string;
      ngay_cap_nhat: string;
      nguoi_cap_nhat: string;
      ngay_hl: string;
      ngay_kt: string;
    }
    // Chi tiết hệ thống chức năng
    interface IChiTietChucNang {
      kieu_ad?: string;
      loai?: string;
      ma?: string;
      ma_doi_tac?: string;
      ten?: string;
      trang_thai?: string;
      trang_thai_ten?: string;
      nhom_cn?: {
        ma?: string;
        ten?: string;
        ma_doi_tac?: string;
        sl_chuc_nang?: number;
        stt?: number;
        trang_thai?: string;
      };
    }
    // Chi tiết hệ thống chức năng
    interface IChiTietNhomChucNang {
      ds_cn?: Array<{
        ma?: string;
        ten?: string;
        loai?: string;
      }>;
      ds_cn_chua_xd?: Array<{
        ma?: string;
        ten?: string;
        loai?: string;
      }>;
      nhom_cn?: {
        ma?: string;
        ten?: string;
        sl_chuc_nang?: number;
        stt?: number;
        trang_thai?: string;
        trang_thai_ten?: string;
      };
    }

    // Danh sách nhóm chức năng phân trang
    interface IDanhSachNhomChucNangPhanTrang {
      ten: string;
      ma_doi_tac: string;
      doi_tac_ten_tat?: string;
      ma_chi_nhanh: string;
      chi_nhanh_ten_tat?: string;
      phong: string;
      phong_ten: string;
      ma?: string;
      stt: number;
      sott: number;
      trang_thai: string;
      trang_thai_ten: string;
      dthoai: string;
      email: string;
      ngay_tao: string;
      nguoi_tao: string;
      ngay_cap_nhat: string;
      nguoi_cap_nhat: string;
      ngay_hl: string;
      ngay_kt: string;
    }

    interface IChiTietChucDanh {
      ma_doi_tac?: string; // đối tác
      ma_doi_tac_ql?: string;
      doi_tac_ql_ten_tat?: string;
      ma?: string;
      ten?: string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
    }
    //Bảng mã bệnh
    interface IDanhMucBangMaBenh {
      ma?: string;
      ten?: string;
      ma_doi_tac?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      // Các field cho dropdown/select
      label?: string;
      value?: string;
    }
    //Nhóm mã bệnh
    interface IDanhMucNhomMaBenh {
      ma?: string;
      ten?: string;
      ma_doi_tac?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      // Các field cho dropdown/select
      label?: string;
      value?: string;
    }
    // mã bệnh ICD
    interface IDanhMucMaBenhICD {
      ma?: string;
      ten?: string;
      ma_doi_tac?: string;
      ten_e?: string;
      ma_ct?: string;
      ma_byt?: string;
      ma_nhom_benh?: string;
      bang_ma_benh?: string;
      benh_db?: string;
      benh_bs?: string;
      benh_td?: string;
      benh_cs?: string;
      nd_tim?: string;
      ghi_chu?: string;
      dinh_nghia?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      // Các field cho dropdown/select
      label?: string;
      value?: string;
    }
    //BHXH loại hộ gia đình
    interface ILoaiHoGiaDinh {
      ma?: string;
      ten?: string;
      ma_doi_tac?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      // Các field cho dropdown/select
      label?: string;
      value?: string;
    }
    //BHXH đơn vị thu hộ
    interface IDonViThuHo {
      ma?: string;
      ten?: string;
      ten_tat?: string;
      mst?: string;
      dchi?: string;
      dthoai?: string;
      ten_e?: string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
      label?: string;
      value?: string;
    }
    //BHXH tài khoản đơn vị thu hộ
    interface ITaiKhoanDonViThuHo {
      ma_dvi?: string;
      ma?: string;
      ten?: string;
      mat_khau?: string;
      dthoai?: string;
      email?: string;
      ngay_hl?: number | string;
      ngay_kt?: number | string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
      label?: string;
      value?: string;
    }
    //Châu lục
    interface IDanhMucChauLuc {
      ma?: string;
      ten?: string;
      ma_doi_tac?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      // Các field cho dropdown/select
      label?: string;
      value?: string;
    }
    //Khu vực
    interface IDanhMucKhuVuc {
      ma?: string;
      ten?: string;
      ma_doi_tac?: string;
      ma_chau_luc?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      // Các field cho dropdown/select
      label?: string;
      value?: string;
    }
    //Quốc gia
    interface IDanhMucQuocGia {
      ma?: string;
      ten?: string;
      ma_doi_tac?: string;
      ma_chau_luc?: string;
      ma_khu_vuc?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      // Các field cho dropdown/select
      label?: string;
      value?: string;
    }
    //Danh mục bệnh viện
    interface IDanhMucBenhVien {
      ma_doi_tac?: string;
      ma?: string;
      ten?: string;
      mst?: string;
      dia_chi?: string;
      dthoai?: string;
      email?: string;
      nhom_bv?: string;
      ten_nhom_bv?: string;
      loai?: string;
      ten_loai?: string;
      tinh_thanh?: string;
      ad_bhyt?: string;
      bl_nt?: string;
      bl_gt?: string;
      bl_ra?: string;
      tk_ngan_hang?: string;
      tk_chi_nhanh?: string;
      tk_so?: string;
      tk_ten?: string;
      nguoi_lhe?: string;
      dthoai_lhe?: string;
      email_lhe?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      // Các field cho dropdown/select
      label?: string;
      value?: string;
    }
    // ========== RESPONSE INTERFACES CHO DANH MUC TINH THANH ==========

    // Interface chung cho tất cả các loại response tỉnh thành
    interface IDanhMucTinhThanh {
      ngay_ad?: number | string;
      ngay_ad_ten?: string;
      ma?: string;
      ten?: string;
      ma_doi_tac?: string;
      mien?: string | null;
      nsd?: string | null;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      // Các field cho dropdown/select
      label?: string;
      value?: string;
    }
    // ========== RESPONSE INTERFACES CHO DANH MUC QUẬN HUYỆN ==========
    interface IDanhMucQuanHuyen {
      ma_tinh?: string;
      ngay_ad?: number | string;
      ngay_ad_ten?: string;
      ma?: string;
      ten?: string;
      ten_tinh?: string;
      trang_thai?: string;
      trang_thai_ten?: string;
      stt?: number;
      postcode?: string;
      sott?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      // Các field cho dropdown/select
      label?: string;
      value?: string;
    }
    // ========== RESPONSE INTERFACES CHO DANH MUC PHƯỜNG XÃ ==========
    interface IDanhMucPhuongXa {
      ma_doi_tac?: string;
      ma_tinh?: string;
      ten_tinh?: string;
      ma_quan?: string;
      ten_quan?: string;
      ngay_ad?: number | string;
      ngay_ad_ten?: string;
      ma?: string;
      ten?: string;
      stt?: number;
      trang_thai?: string;
      postcode?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
    }

    //KHÁCH HÀNG
    interface IKhachHang {
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      ma_chi_nhanh_ql?: string;
      loai_kh?: string;
      ma?: string;
      ma_kh?: string;
      ten?: string;
      dchi?: string;
      mst?: string;
      cmt?: string;
      dthoai?: string;
      ten_loai_kh?: string;
      email?: string;
      nguoi_lhe?: string;
      dthoai_lhe?: string;
      email_lhe?: string;
      nd_tim?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
    }

    //Danh sách hợp đồng xcg phân trang
    interface IDanhSachHopDongXePhanTrang {
      ten: string;
      ma_doi_tac: string;
      doi_tac_ten_tat?: string;
      ma_chi_nhanh: string;
      chi_nhanh_ten_tat?: string;
      phong: string;
      phong_ten: string;
      ma?: string;
      stt: number;
      sott: number;
      trang_thai: string;
      trang_thai_ten: string;
      dthoai: string;
      email: string;
      ngay_tao: string;
      nguoi_tao: string;
      ngay_cap_nhat: string;
      nguoi_cap_nhat: string;
      ngay_hl: string;
      ngay_kt: string;
      kieu_hd: string;
    }
    //Danh mục đại lý
    interface IDanhMucDaiLy {
      ma?: string;
      ma_doi_tac_ql?: string;
      ma_doi_tac?: string;
      ten?: string;
      nguoi_dd?: string;
      loai?: string;
      dthoai_dd?: string;
      email_dd?: string;
      mst_dd?: string;
      cmt_dd?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai?: string;
      trang_thai_ten?: string;
      stt?: number;
      ma_ct?: string;
      ten_loai?: string;
      doi_tac_ql_ten_tat?: string;
    }
    interface IChiTietDanhMucDaiLy {
      dl?: {
        ma?: string;
        // ma_doi_tac_ql?: string;
        ma_doi_tac?: string;
        ten?: string;
        nguoi_dd?: string;
        loai?: string;
        dthoai_dd?: string;
        email_dd?: string;
        mst_dd?: string;
        cmt_dd?: string;
        ngay_tao?: string;
        nguoi_tao?: string;
        ngay_cap_nhat?: string;
        nguoi_cap_nhat?: string;
        trang_thai?: string;
        trang_thai_ten?: string;
        stt?: number;
        ma_ct?: string;
        ten_loai?: string;
      };

      // doi_tac_ql_ten_tat?: string;
      dl_qly?: Array<{
        is_checked: string;
        ma_dly?: string;
        ma_chi_nhanh_ql?: string;
        ma_doi_tac_ql?: string;
        ten_chi_nhanh_ql?: string;
        ten_doi_tac_ql?: string;
        nd_tim?: string;
      }>;
    }
    interface IChiTietPhuongThucKhaiThac {
      doi_tac_ql_ten_tat?: string;
      ma?: string;
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      ngay_cap_nhat?: string;
      ngay_tao?: string;
      nguoi_cap_nhat?: string;
      nguoi_tao?: string;
      sott?: string;
      stt?: string | number;
      ten?: string;
      trang_thai?: string;
      trang_thai_ten?: string;
    }
    //DANH SÁCH KHAI THÁC
    interface IDanhSachPhuongThucKhaiThac {
      ten: string;
      ma_doi_tac: string;
      doi_tac_ten_tat?: string;
      ma_chi_nhanh: string;
      chi_nhanh_ten_tat?: string;
      phong: string;
      phong_ten: string;
      ma?: string;
      stt: number;
      sott: number;
      trang_thai: string;
      trang_thai_ten: string;
      dthoai: string;
      email: string;
      ngay_tao: string;
      nguoi_tao: string;
      ngay_cap_nhat: string;
      nguoi_cap_nhat: string;
      ngay_hl: string;
      ngay_kt: string;
      ma_doi_tac_ql;
    }

    //
    interface IDoiTacNhanVien {
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      ma_chi_nhanh_ql?: string;
      phong_ql?: string;
      ma?: string;
      ten?: string;
      cmt?: string;
      dthoai?: string;
      email?: string;
      dchi?: string;
      nd_tim?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
      label?: string;
      value?: string;
    }

    interface ISanPham {
      ma?: string;
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      doi_tac_ql_ten_tat?: string; //nxh
      nv?: string;
      ten_nv?: string; //nxh
      stt?: number;
      ten?: string;
      trang_thai?: string;
      label?: string;
      value?: string;
      nhom?: string;
    }

    interface IChuongTrinhBaoHiem {
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      doi_tac_ql_ten_tat?: string;
      ma?: string;
      ten?: string;
      stt?: number;
      trang_thai?: string;
      nv?: string;
      ngay_ad?: string | number;
      ngay_ad_tim?: number;
      ten_nv?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
      label?: string;
      value?: string;
      id?: number;
      ma_sp?: string;
    }

    interface INhaBaoHiemTPA {
      ma?: string;
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      ten?: string;
      label?: string;
      value?: string;
      nv?: string;
    }
    interface IDanhMucSanPham {
      ma?: string;
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      doi_tac_ql_ten_tat?: string;
      nv?: string;
      ten_nv?: string; //nxh
      stt?: number;
      ten?: string;
      trang_thai?: string;
      ngay_tao: string;
      nguoi_tao: string;
      ngay_cap_nhat: string;
      nguoi_cap_nhat: string;
      trang_thai_ten: string;
      nv_ten?: string;
    }
    interface IPhuongThucKhaiThac {
      ma?: string;
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      ten?: string;
      label?: string;
      value?: string;
    }
    interface IHopDongXe {
      ma_doi_tac?: string;
      ma_chi_nhanh?: string;
      phong?: string;
      ma_doi_tac_ql?: string;
      ten_doi_tac_ql?: string;
      ma_chi_nhanh_ql?: string;
      ten_chi_nhanh_ql?: string;
      phong_ql?: string;
      ten_phong_ql?: string | null;
      ma_sp?: string;
      ten_sp?: string;
      ma_ctbh?: string;
      ten_ctbh?: string | null;
      ma_cb_ql?: string;
      ten_cb_ql?: string;
      so_id?: number;
      so_id_dt?: number;
      so_id_g?: number;
      so_id_d?: number;
      so_hd?: string;
      so_hd_g?: string | null;
      so_hd_d?: string;
      so_hd_dtac?: string | null;
      kieu_hd?: string;
      nv?: string;
      ngay_cap?: number;
      ngay_cap_date?: string;
      gio_hl?: string;
      ngay_hl?: number;
      ngay_hl_date?: string;
      gio_kt?: string;
      ngay_kt?: number;
      ngay_kt_date?: string;
      ma_kh?: string;
      ten_kh?: string;
      mst_kh?: string;
      dthoai_kh?: string | null;
      email_kh?: string;
      sl_dtuong?: number;
      nt_phi?: string;
      ty_gia?: number;
      tong_phi?: number;
      tong_phi_nt?: number;
      pt_kt?: string;
      daily_kt?: string;
      tl_thue?: number;
      tien_thue?: number;
      vip?: string;
      ngay_huy?: string;
      nd_huy?: string | null;
      nd_tim_1?: string;
      nd_tim_2?: string | null;
      ngay_trinh?: string;
      nguoi_trinh?: string | null;
      ngay_duyet?: string;
      ngay_duyet_dtac?: string;
      ngay_duyet_dtac_num?: number;
      nguoi_duyet?: string | null;
      ngay_ht?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai?: string;
      ma_nha_tpa?: string;
      ngay_duyet_num?: number;
      ngay_huy_num?: number;
      kieu_hd_ten?: string;
      trang_thai_ten?: string;
      ngay_trinh_num?: number;
      file_hd?: string;
      file_gcn?: string;
    }
    interface IDanhSachBoMaQuyenLoi {
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      doi_tac_ql_ten_tat?: string;
      nv?: string;
      ma?: string;
      ten?: string;
      ma_sp?: string;
      ten_sp?: string;
      loai?: string;
      ten_loai?: string;
      qloi_gh?: string;
      ten_qloi_gh?: string;
      ma_ct?: string;
      bl_nt?: string;
      bl_gt?: string;
      bl_ra?: string;
      ma_dtac?: string;
      stt?: number;
      trang_thai?: string;
      ten_nv?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
      cap: number;
      ma_sp?: string;
    }
    interface IChiTietBoMaQuyenLoi {
      ma?: string;
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      nv?: string;
      stt?: number;
      ten?: string;
      ma_sp?: string;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      bl_nt?: string;
      bl_gt?: string;
      bl_ra?: string;
      sott?: number;
      ma_ct?: string;
    }

    export interface IHopDongConNguoi {
      sott?: number;
      ma_doi_tac?: string;
      ma_chi_nhanh?: string;
      phong?: string;
      ma_doi_tac_ql?: string;
      ten_doi_tac_ql?: string;
      ma_chi_nhanh_ql?: string;
      ten_chi_nhanh_ql?: string;
      phong_ql?: string;
      ma_sp?: string;
      ten_sp?: string;
      ma_ctbh?: string;
      ten_ctbh?: string;
      ten_phong_ql?: string;
      ma_cb_ql?: string;
      ten_cb_ql?: string;
      so_id: string | number;
      so_id_g?: number;
      so_id_d?: number;
      so_hd?: string;
      so_hd_g?: string;
      so_hd_d?: string;
      so_hd_dtac?: string;
      kieu_hd?: string;
      nv?: string;
      ngay_cap?: string;
      ngay_cap_date?: string;
      gio_hl?: string;
      ngay_hl?: number;
      ngay_hl_date?: string;
      gio_kt?: string;
      ngay_kt?: number;
      ngay_kt_date?: string;
      ma_kh?: string;
      ten_kh?: string;
      mst_kh?: string;
      dthoai_kh?: string;
      email_kh?: string;
      sl_dtuong?: number;
      nt_phi?: string;
      ty_gia?: number;
      tong_phi?: number | string;
      tong_phi_nt?: number;
      pt_kt?: string;
      daily_kt?: string;
      ten_daily_kt?: string;
      tl_thue?: number;
      tien_thue?: number;
      vip?: string;
      ngay_huy?: string;
      nd_huy?: string;
      nd_tim_1?: string;
      nd_tim_2?: string;
      ngay_trinh?: string;
      ngay_trinh_num?: string;
      nguoi_trinh?: string;
      ngay_duyet?: string;
      ngay_duyet_dtac?: string;
      ngay_duyet_dtac_num?: number;
      nguoi_duyet?: string;
      ngay_ht?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai?: string;
      ma_nha_tpa?: string;
      ngay_duyet_num?: number;
      ngay_huy_num?: number;
      kieu_hd_ten?: string;
      trang_thai_ten?: string;
      file_gcn?: string;
      file_hd?: string;
    }

    interface IDanhSachBoMaNguyenTe {
      ten: string;
      ma_doi_tac: string;
      doi_tac_ten_tat?: string;
      ma_chi_nhanh: string;
      chi_nhanh_ten_tat?: string;
      phong: string;
      phong_ten: string;
      ma?: string;
      stt: number;
      sott: number;
      trang_thai: string;
      trang_thai_ten: string;
      dthoai: string;
      email: string;
      ngay_tao: string;
      nguoi_tao: string;
      ngay_cap_nhat: string;
      nguoi_cap_nhat: string;
      ngay_hl: string;
      ngay_kt: string;
      ma_doi_tac_ql;
    }
    interface IChiTietBoMaNguyenTe {
      doi_tac_ql_ten_tat?: string;
      ma?: string;
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      ngay_cap_nhat?: string;
      ngay_tao?: string;
      nguoi_cap_nhat?: string;
      nguoi_tao?: string;
      sott?: string;
      stt?: string | number;
      ten?: string;
      trang_thai?: string;
      trang_thai_ten?: string;
    }
    interface IHeThongMenu {
      ma?: string;
      stt?: number;
      ten?: string;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      nhom?: string;
      sott?: number;
      ma_cha?: string;
      url?: string;
      app_name?: string;
    }
    //Interface loại xe
    interface ILoaiXe {
      ma?: string;
      nv?: string;
      stt?: number;
      ten?: string;
    }
    interface IHangXe {
      ma?: string;
      nv?: string;
      stt?: number;
      ten?: string;
    }
    interface IHieuXe {
      ma?: string;
      nv?: string;
      stt?: number;
      ten?: string;
      hang_xe?: string;
    }

    interface INhomHangMucXe {
      ma?: string;
      ten?: string;
      nv?: string;
      ten_nv?: string;
      ma_doi_tac?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      // Các field cho dropdown/select
      label?: string;
      value?: string;
    }
    interface IHangMucXe {
      ma?: string;
      ten?: string;
      nv?: string;
      ten_nv?: string;
      ma_doi_tac?: string;
      loai?: string;
      nhom?: string;
      vi_tri?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      // Các field cho dropdown/select
      label?: string;
      value?: string;
    }
    //Mức độ tổn thất xe
    interface IMucDoTonThatXe {
      ma?: string;
      ma_doi_tac?: string;
      stt?: number;
      sott?: number;
      ten?: string;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      label?: string?; // sửa
      value?: string?; // sửa
    }
    //Cấu hình mẫu hợp đồng
    interface ICauHinhMauHopDong {
      ma_doi_tac?: string;
      bt?: string;
      ten?: string;
      ma_doi_tac_ql?: string;
      nv?: string;
      ten_nv?: string;
      ma_sp?: string;
      ten_sp?: string;
      ngay_ad?: number | string;
      id_file?: number;
      url_file?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      // Các field cho dropdown/select
      label?: string;
      value?: string;
    }

    //Cấu hình mẫu giấy chứng nhận
    interface ICauHinhMauGCN {
      ma_doi_tac?: string;
      bt?: string;
      ten?: string;
      ma_doi_tac_ql?: string;
      nv?: string;
      ten_nv?: string;
      ma_sp?: string;
      ten_sp?: string;
      ngay_ad?: number | string;
      id_file?: number;
      url_file?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      // Các field cho dropdown/select
      label?: string;
      value?: string;
    }

    interface IDoiTuongBaoHiemXeCoGioi {
      gcn?: {
        bien_xe?: string;
        dchi?: string;
        dkbs?: string | null;
        gcn?: string;
        gia_tri?: number;
        gio_hl?: string;
        gio_kt?: string;
        hang_xe?: string;
        hieu_xe?: string;
        sdbs?: string;
        loai_xe?: string;
        ma_doi_tac?: string;
        md_sd?: string;
        nam_sx?: string;
        nd_1?: string;
        nd_2?: string | null;
        nd_3?: string | null;
        ngay_cap?: number;
        ngay_cap_nhat?: string | null;
        ngay_hl?: number;
        ngay_kt?: number;
        ngay_tao?: string | null;
        nguoi_cap_nhat?: string | null;
        nguoi_tao?: string | null;
        so_cho?: number;
        so_id?: number;
        so_id_dt?: number;
        so_khung?: string;
        so_lphu_xe?: number;
        so_may?: string;
        so_nguoi_bh?: number;
        ten?: string;
        tong_phi?: number | null;
        tong_phi_vnd?: number | null;
        trang_thai?: string | null;
        trong_tai?: number | null;
        vip?: string;
      };
      gcn_dk?: Array<{
        doi_tuong: string;
        key: string;
        ktru: string;
        lh_nv: string;
        loai: string;
        ma?: string;
        ma_doi_tac: string;
        ma_doi_tac_ql: string;
        mien_thuong: number;
        nhom: string;
        nv: string;
        phi: number;
        stt: number;
        ten: string;
        thue: number;
        tien: number;
        phi_giam: number;
        thue_giam: number;
      }>;
      gcn_dkbs?: Array<{
        doi_tuong: string;
        key: string;
        ktru: string;
        lh_nv: string;
        loai: string;
        ma?: string;
        ma_doi_tac: string;
        ma_doi_tac_ql: string;
        mien_thuong: number;
        nhom: string;
        nv: string;
        phi: number;
        stt: number;
        ten: string;
        thue: number;
        tien: number;
        phi_giam: number;
        thue_giam: number;
      }>;
    }
    interface IDanhMucNganHang {
      ma?: string;
      ma_doi_tac?: string;
      stt?: number;
      ten?: string;
      trang_thai?: string;
      ngay_tao: string;
      nguoi_tao: string;
      ngay_cap_nhat: string;
      nguoi_cap_nhat: string;
      trang_thai_ten: string;
      label?: string; // sửa
      value?: string; // sửa
    }

    interface IChiNhanhNganHang {
      ma_doi_tac?: string;
      ma?: string;
      ten?: string;
      ma_ngan_hang?: string;
      ten_ngan_hang?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      label?: string;
      value?: string;
    }

    interface IDanhSachChiNhanhNganHangPhanTrang {
      ma_doi_tac?: string;
      ma?: string;
      ten?: string;
      ma_ngan_hang?: string;
      ten_ngan_hang?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
    }

    interface IChiTietChiNhanhNganHang {
      ma_doi_tac?: string;
      ma?: string;
      ten?: string;
      ma_ngan_hang?: string;
      ten_ngan_hang?: string;
      stt?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
    }
    interface IDanhMucHangXe {
      ma?: string;
      ten?: string;
      nv?: string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      nv_ten?: string;
      sott?: number;
    }
    interface IChiTietDanhMucHangXe {
      ma?: string;
      ten?: string;
      nv?: string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      nv_ten?: string;
      sott?: number;
    }
    interface IDanhMucHieuXe {
      ma?: string;
      ten?: string;
      nv?: string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      nv_ten?: string;
      sott?: number;
      hang_xe?: string;
    }
    interface IChiTietDanhMucHieuXe {
      ma?: string;
      ten?: string;
      nv?: string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      nv_ten?: string;
      sott?: number;
      hang_xe?: number;
    }
    interface IGoiBaoHiem {
      ma_ctbh?: string;
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      key?: string;
      id?: number;
      nv?: string;
      ma_sp?: string;
      ten_sp?: string;
      ma_goi_bh?: string;
      ten?: string;
      ma_ct?: string;
      gioi_tinh?: string;
      do_tuoi?: string;
      ten_doi_tac_ql?: string;
      tuoi_tu?: number;
      tuoi_toi?: number;
      ngay_ad?: string | number;
      stt?: number;
      trang_thai?: string;
      ngay_ad_num?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
    }
    interface ILoaiHinhNghiepVuXeCoGioi {
      lh_nv?: string;
      key?: string;
      doi_tuong?: string;
      loai?: string;
      ma?: string;
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      nhom?: string;
      nv?: string;
      stt?: number;
      ten?: string;
      tien?: number;
      phi?: number;
      thue: number;
      mien_thuong?: number;
      ktru?: string;
      tong_phi?: number;
      ma_sp?: string;
      phi_giam: number;
      thue_giam: number;
    }
    interface IGoiBaoHiemConNguoi {
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      id: number;
      key?: string;
      // id: number;
      nv?: string;
      ma_sp?: string;
      ten_sp?: string;
      ma?: string;
      ten?: string;
      ma_ct?: string;
      gioi_tinh?: string;
      do_tuoi?: string;
      ten_doi_tac_ql?: string;
      tuoi_tu?: number;
      tuoi_toi?: number;
      ngay_ad?: string | number;
      stt?: number;
      trang_thai?: string;
      ngay_ad_num?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
    }

    interface IChiTietGoiBaoHiemConNguoi {
      goi_bh: {
        ma_doi_tac?: string;
        ma_doi_tac_ql?: string;
        id?: number;
        nv?: string;
        ma_sp?: string;
        ma?: string;
        ten?: string;
        ma_ct?: string;
        gioi_tinh?: string;
        tuoi_tu?: number;
        tuoi_toi?: number;
        ngay_ad?: number | string;
        stt?: number;
        trang_thai?: string;
        ngay_ad_date?: string;
      };
      //GÓI BẢO HIỂM CẤP TRÊN
      goi_bh_ct: Array<{
        ma_doi_tac?: string;
        id_goi?: number;
        ma_qloi: string;
        ma_qloi_ct?: string;
        ten_qloi: string;
        gh_lan_ngay?: number;
        gh_tien_lan_ngay?: number;
        gh_lan_ngay_dtri?: number;
        gh_tien_lan_ngay_dtri?: number;
        gh_tien_nam?: number;
        tgian_cho?: number;
        ma_qloi_phu_thuoc?: string;
        ma_qloi_tru_lui?: string;
        kieu_ad?: string;
        kieu_ad_ten?: string;
        qloi_gh?: string;
        phi_bh?: number;
        ghi_chu?: string;
        cap: number;
      }>;
      // KHÔNG DÙNG ĐẾN
      goi_bh_dct: Array<{
        ma_doi_tac?: string;
        id_goi?: number;
        ma_bv?: string;
        ma_qloi?: string;
        ma_benh?: string;
        ty_le?: number;
        tgian_cho?: number;
        tien_toi_da?: number;
        ghi_chu?: string;
      }>;
      // GÓI BẢO HIỂM ĐIỀU KHOẢN BỔ SUNG
      goi_bh_dkbs: Array<any>;
    }

    // ===== DANH MỤC LOẠI XE =====

    interface IDanhMucLoaiXe {
      ma?: string;
      ten?: string;
      nv?: string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      nv_ten?: string;
      sott?: number;
      label?: string;
      value?: string;
    }

    interface IDanhSachDanhMucLoaiXePhanTrang {
      ma?: string;
      ten?: string;
      nv?: string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      nv_ten?: string;
      sott?: number;
    }

    interface IChiTietDanhMucLoaiXe {
      ma?: string;
      ten?: string;
      nv?: string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      nv_ten?: string;
      sott?: number;
    }

    interface IThongTinThanhToanCuaHopDong {}
    interface IKyThanhToan {
      so_id_ky_ttoan?: number;
      so_id_d?: number;
      ky_tt?: number;
      so_tien?: number;
      bt?: number;
      ky_tt_date?: string;
      loai?: string;
      loai_ten?: string | null;
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      ngay_cap_nhat?: string | null;
      ngay_tao?: string | null;
      ngay_tt?: string | null;
      ngay_tt_date?: string;
      nguoi_cap_nhat?: string | null;
      nguoi_tao?: string | null;
      so_hd?: string | null;
      so_id?: number | null;
      so_id_g?: number | null;
      so_tien_da_tt?: number | null;
      trang_thai?: string | null;
      key?: string;
      sott?: number;
      ky_ttoan?: {
        ky_tt?: string;
        ky_tt_num?: number;
        ma_doi_tac?: string;
        ma_doi_tac_ql?: string;
        ngay_cap_nhat?: string;
        ngay_tao?: string;
        nguoi_cap_nhat?: string;
        nguoi_tao?: string;
        so_id_d?: number;
        so_id_ky_ttoan?: number;
        so_tien?: number;
        so_tien_da_tt?: number;
        sott: number;
      };
      ky_ttoan_ct?: Array<{
        key?: string;
        stt?: number;
        ngay_tt?: string;
        so_tien_da_tt?: number;
        bt?: number;
      }>;
    }
    interface IDonViDongTai {
      dong_bh?: string;
      ma?: string;
      tai_bh?: string;
      ten?: string;
    }
    interface IDongBaoHiem {
      kieu_dong?: string;
      loai_dong?: string;
      loai_dong_ten?: string;
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      ma_dvi_dong?: string;
      nv?: string;
      sl_dt_bh?: number;
      sl_goi_bh?: number;
      sl_qloi_bh?: number;
      tl_dong?: number;
      sott: number;
      ma_dvi_dong?: string;
    }
    interface ITaiBaoHiem {
      kieu_dong?: string;
      loai_dong?: string;
      loai_dong_ten?: string;
      ma_doi_tac?: string;
      ma_doi_tac_ql?: string;
      ma_dvi_dong?: string;
      nv?: string;
      sl_dt_bh?: number;
      sl_goi_bh?: number;
      sl_qloi_bh?: number;
      tl_dong?: number;
      sott: number;
      ma_dvi_tai?: string;
    }
    interface IMenuNSD {
      is_checked?: number | string;
      ma?: string;
      ma_cha?: string;
      ma_doi_tac?: string;
      nd_tim?: string;
      nhom?: string;
      ten?: string;
      url?: string;
      stt?: number;
    }
    interface IChucNangNSD {
      is_checked?: number | string;
      ma_chuc_nang?: string;
      hanh_dong?: string;
      nd_tim?: string;
      ten_chuc_nang?: string;
      loai?: string;
    }
    interface IDonViQuanLyNSD {
      is_checked?: number | string;
      ma?: string;
      ma_cha?: string;
      ma_chi_nhanh_ql?: string;
      ma_doi_tac_ql?: string;
      ten_chi_nhanh_ql?: string;
      ten_doi_tac_ql?: string;
      nd_tim?: string;
    }
    interface IVaiTroChucNang {
      ma?: string;
      ten?: string;
      trang_thai?: string;
      ma_doi_tac?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
      stt?: number;
      sl_chuc_nang?: string;
    }
    interface INhomChucNang {
      ma?: string;
      ten?: string;
      trang_thai?: string;
    }
    interface INguoiPheDuyetHopDong {
      ma_doi_tac?: string;
      nsd_duyet?: string;
      nhom_duyet?: string;
      ma_doi_tac_ql?: string;
      ma_chi_nhanh_ql?: string;
      nv?: string;
      ma_sp?: string;
      ngay_ad?: number | string;
      tien_tu?: number;
      tien_toi?: number;
      ten_nsd_duyet?: string;
      key?: string;
      sott?: number;
      bt?: number;
    }
    interface ITrinhPheDuyetHopDong {
      so_id?: number;
      nv?: string;
      nsd_duyet?: string;
      nd_trinh?: string;
    }
    interface IHopDongTrinhDuyetXCG {
      key?: string;
      bt?: number;
      kieu_hd?: string;
      kieu_hd_ten?: string;
      lan_trinh?: number;
      ma_chi_nhanh_duyet?: string;
      ma_chi_nhanh_ql?: string;
      ma_doi_tac?: string;
      ma_doi_tac_duyet?: string;
      ma_doi_tac_ql?: string;
      ma_kh?: string;
      ma_sp?: string;
      nd_duyet?: string | null;
      nd_tchoi?: string | null;
      nd_tra_lai?: string | null;
      nd_trinh?: string;
      ngay_duyet?: string;
      ngay_tchoi?: string;
      ngay_tra_lai?: string;
      ngay_trinh?: string;
      nsd_duyet?: string;
      nsd_trinh?: string;
      nv?: string;
      phong_ql?: string;
      so_hd?: string;
      so_id?: number;
      sott?: number;
      stt?: number | string;
      ten_chi_nhanh_ql?: string;
      ten_kh?: string;
      ten_sp?: string;
      tong_phi?: number;
      trang_thai?: string;
      trang_thai_ten?: string;
      lke?: any;
    }
    interface INhomChucNang {
      ma?: string;
      ten?: string;
      trang_thai?: string;
      ma_doi_tac?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
      stt?: number;
      sl_chuc_nang?: string;
    }
    interface IFileThumbnail {
      ma_doi_tac?: string;
      so_id?: number;
      nv?: string;
      id_file?: number;
      url_file?: string;
      so_id_dt?: number;
      ma_hang_muc?: string;
      ten_hang_muc?: string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
    }
    interface ICauHinhPhanCapPheDuyet {
      nsd_duyet?: string;
      ngay_ad?: number | string;
      bt?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
    }
    interface ICauHinhPhanCapPheDuyetCT {
      bt_phan_cap?: number;
      nhom_duyet?: string;
      bt?: number;
      ma_doi_tac_ql?: string;
      ten_sp?: string;
      ma_sp?: string;
      doi_tac_ql_ten_tat?: string;
      chi_nhanh_ql_ten_tat?: string;
      ten_nhom?: string;
      ma_chi_nhanh_ql?: string;
      tien_tu?: number;
      tien_toi?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai?: string;
    }
    interface ICauHinhPhanCapNhom {
      ma?: string;
      ten?: string;
      ten_tat?: string;
      trang_thai?: string;
      stt?: string;
    }

    export interface INguoiDuocBaoHiemHopDongConNguoi {
      ma_doi_tac?: string;
      so_id: string | number;
      so_id_dt: string | number;
      gcn?: string;
      gcn_dtac?: string;
      ma?: string;
      ten?: string;
      ma_dtac?: string;
      dia_chi?: string;
      ngay_sinh?: string;
      gioi_tinh?: string;
      so_cmt?: string;
      dthoai?: string;
      email?: string;
      ma_sp?: string;
      sdbs?: string;
      ten_sp?: string;
      ma_goi_bh?: string;
      ten_goi_bh?: string;
      vip?: string;
      ngay_cap?: string;
      gio_hl?: string;
      ngay_hl?: string;
      gio_kt?: string;
      ngay_kt?: string;
      ngay_duyet?: string;
      cty_ctac?: string;
      cnhanh_ctac?: string;
      pban_ctac?: string;
      ma_nv_ctac?: string;
      cvu_ctac?: string;
      email_ctac?: string;
      tong_phi?: string;
      tong_phi_vnd?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      sott?: number;
    }

    export interface IDieuKhoanQuyenLoiConNguoi {
      ma_doi_tac?: string;
      id_goi?: number;
      ma_qloi?: string;
      ma_qloi_ct?: string;
      ten_qloi?: string;
      gh_lan_ngay?: number;
      gh_tien_lan_ngay?: number;
      gh_lan_ngay_dtri?: number;
      gh_tien_lan_ngay_dtri?: number;
      gh_tien_nam?: number;
      tgian_cho?: number;
      ma_qloi_phu_thuoc?: string;
      ma_qloi_tru_lui?: string;
      kieu_ad?: string;
      kieu_ad_ten?: string;
      qloi_gh?: string;
      phi_bh?: number;
      ghi_chu?: string;
      cap?: number;
      tl_dct?: number;
      nt_tien_bh?: string;
    }
    interface INhomPhanCapDuyet {
      ma?: string;
      stt?: number;
      ten?: string;
      trang_thai?: string;
    }
    interface ICauHoiApDung {
      bt?: number;
      ma?: string;
      ten?: string;
      ma_doi_tac_ql?: string;
      nv?: string;
      ma_sp?: string;
      ngay_ad?: number | string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
    }
    interface ICauHoi {
      ma?: string;
      ten?: string;
      bt_ap_dung?: number;
      kieu_chon?: string;
      bat_buoc?: string;
      do_rong?: string;
      stt?: number;
      trang_thai?: string;
      ch_ct?: Array<{
        gia_tri?: string;
        ten_gia_tri?: string;
        mac_dinh?: string;
      }>;
    }
    interface IChiTietNguoiPhuThuoc {
      ma_doi_tac?: string;
      so_id?: number;
      so_id_dt?: number;
      bt?: number;
      ten?: string;
      ngay_sinh?: string;
      ngay_sinh_date?: string;
      gioi_tinh?: string;
      so_cmt?: string;
      dthoai?: string;
      email?: string;
      moi_qhe?: string;
      stt?: number;
      trang_thai?: string;
      gioi_tinh_ten?: string;
      moi_qhe_ten?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
    }
    interface ICauHoiCT {
      bt_ap_dung?: number;
      ma_cau_hoi?: string;
      gia_tri?: string;
      ten_gia_tri?: string;
      mac_dinh?: string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      sott?: number;
    }
    interface ICauHinhTyLeHoaHong {
      ma_dai_ly?: string;
      ma_doi_tac_ql?: string;
      ma_chi_nhanh_ql?: string;
      ngay_ad?: string | number;
      tlhh?: number;
      tlhh_ct?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      doi_tac_ql_ten_tat?: string;
      ten_dai_ly?: string;
    }
    interface ICayTyLeHoaHong {
      ma_dai_ly?: string;
      ma_doi_tac_ql?: string;
      tlhh?: number;
      tlhh_ct?: number;
      doi_tac_ql_ten_tat?: string;
      ten_dai_ly?: string;
      cap?: string;
    }
    interface IChucNangTheoVaiTro {
      ma?: string;
      ten?: string;
      trang_thai?: string;
      trang_thai_ten?: string;
      stt?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
    }
    interface IChiTietChucNangTheoVaiTro {
      ds_cn?: Array<{
        ma?: string;
        ten?: string;
        loai?: string;
      }>;
      ds_cn_chua_xd?: Array<{
        ma?: string;
        ten?: string;
        loai?: string;
      }>;
      vtro_cn?: {
        ma?: string;
        ten?: string;
        sl_chuc_nang?: number;
        stt?: number;
        trang_thai?: string;
        trang_thai_ten?: string;
      };
    }
    interface IDanhMucNghiepVu {
      ma?: string;
      ma_doi_tac?: string;
      stt?: number;
      ten?: string;
      trang_thai?: string;
      ngay_tao: string;
      nguoi_tao: string;
      ngay_cap_nhat: string;
      nguoi_cap_nhat: string;
      trang_thai_ten: string;
    }
    interface INhomDoiTuong {
      ma?: string;
      ten?: string;
      ma_cha?: string;
      ma_dau?: string;
      mo_ta?: string;
      trang_thai?: string;
      stt?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
    }
    interface IBaoHiemTaiSan {
      kieu_hd: string;
      kieu_hd_ten: string;
      ma_chi_nhanh_ql: string;
      ma_doi_tac: string;
      ma_doi_tac_ql: string;
      ma_kh: string;
      ngay_cap: string; // "30/07/2025"
      ngay_cap_nhat: string; // "01/08/2025 14:34:20"
      ngay_hl: string; // "30/07/2025"
      ngay_kt: string; // "30/07/2026"
      ngay_tao: string; // "30/07/2025 09:27:50"
      nguoi_cap_nhat: string;
      nguoi_tao: string;
      nv: string;
      nv_ten: string;
      sl_dtuong: number;
      so_hd: string;
      so_hd_d: string;
      so_hd_g: string | null;
      so_id: number;
      sott: number;
      ten_chi_nhanh_ql: string;
      ten_doi_tac_ql: string;
      ten_kh: string;
      ten_sp: string;
      tong_phi: number;
      trang_thai: string;
      trang_thai_ten: string;
      vip: string;
    }
    interface IChiTietNhomDoiTuong {
      ma?: string;
      ten?: string;
      ma_cha?: string;
      ma_dau?: string;
      mo_ta?: string;
      trang_thai?: string;
      stt?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai_ten?: string;
      lke_ct?: Array<{
        ma_thuoc_tinh?: string;
        ten_thuoc_tinh?: string;
        kieu_dl?: string;
      }>;
    }
    // BỆNH VIỆN
    interface IBenhVien {
      id?: number;
      ma?: string;
      ten?: string;
      dia_chi?: string;
      dien_thoai?: string;
      email?: string;
      trang_thai?: string;
      trang_thai_ten?: string;
      hinh_thuc_ap_dung?: string;
      is_selected?: boolean;
      stt?: number;
      ngay_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_tao?: string;
      nguoi_cap_nhat?: string;
    }
    interface IChiTietHopDongTaiSan {
      ma_doi_tac?: string;
      ma_chi_nhanh?: string;
      phong?: string;
      ma_doi_tac_ql?: string;
      ten_doi_tac_ql?: string;
      ma_chi_nhanh_ql?: string;
      ten_chi_nhanh_ql?: string;
      phong_ql?: string;
      ten_phong_ql?: string | null;
      ma_sp?: string;
      ten_sp?: string;
      ma_ctbh?: string;
      ten_ctbh?: string | null;
      ma_cb_ql?: string;
      ten_cb_ql?: string;
      so_id?: number;
      so_id_dt?: number;
      so_id_g?: number;
      so_id_d?: number;
      so_hd?: string;
      so_hd_g?: string | null;
      so_hd_d?: string;
      so_hd_dtac?: string | null;
      kieu_hd?: string;
      nv?: string;
      ngay_cap?: number;
      ngay_cap_date?: string;
      gio_hl?: string;
      ngay_hl?: number;
      ngay_hl_date?: string;
      gio_kt?: string;
      ngay_kt?: number;
      ngay_kt_date?: string;
      ma_kh?: string;
      ten_kh?: string;
      mst_kh?: string;
      dthoai_kh?: string | null;
      email_kh?: string;
      sl_dtuong?: number;
      nt_phi?: string;
      ty_gia?: number;
      tong_phi?: number;
      tong_phi_nt?: number;
      pt_kt?: string;
      daily_kt?: string;
      tl_thue?: number;
      tien_thue?: number;
      vip?: string;
      ngay_huy?: string;
      nd_huy?: string | null;
      nd_tim_1?: string;
      nd_tim_2?: string | null;
      ngay_trinh?: string;
      nguoi_trinh?: string | null;
      ngay_duyet?: string;
      ngay_duyet_dtac?: string;
      ngay_duyet_dtac_num?: number;
      nguoi_duyet?: string | null;
      ngay_ht?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai?: string;
      ma_nha_tpa?: string;
      ngay_duyet_num?: number;
      ngay_huy_num?: number;
      kieu_hd_ten?: string;
      trang_thai_ten?: string;
      ngay_trinh_num?: number;
      file_hd?: string;
      file_gcn?: string;
    }
    interface IDoiTuongBaoHiemTaiSan {
      gcn?: {
        nhom_dt?: string;
        latitude?: number;
        longitude?: number;
        dkbs?: string | null;
        gcn?: string;
        gia_tri?: number;
        gio_hl?: string;
        gio_kt?: string;
        sdbs?: string;
        ma_doi_tac?: string;
        nd_1?: string;
        nd_2?: string | null;
        nd_3?: string | null;
        ngay_cap?: number;
        ngay_cap_nhat?: string | null;
        ngay_hl?: number;
        ngay_kt?: number;
        ngay_tao?: string | null;
        nguoi_cap_nhat?: string | null;
        nguoi_tao?: string | null;
        so_cho?: number;
        so_id?: number;
        so_id_dt?: number;
        ten?: string;
        tong_phi?: number | null;
        tong_phi_vnd?: number | null;
        trang_thai?: string | null;
        trong_tai?: number | null;
        vip?: string;
        dchi?: string;
        tinh_thanh?: string;
        phuong_xa?: string;
        dt_ttrr?: string;
      };
      gcn_ct?: Array<{
        ma_thuoc_tinh?: string;
        ten_thuoc_tinh?: string;
        kieu_dl?: string;
        gia_tri?: string;
      }>;
      gcn_dk?: Array<{
        doi_tuong: string;
        key: string;
        ktru: string;
        lh_nv: string;
        loai: string;
        ma?: string;
        ma_doi_tac: string;
        ma_doi_tac_ql: string;
        mien_thuong: number;
        nhom: string;
        nv: string;
        phi: number;
        stt: number;
        ten: string;
        thue: number;
        tien: number;
        phi_giam: number;
        thue_giam: number;
      }>;
      gcn_dkbs?: Array<{
        doi_tuong: string;
        key: string;
        ktru: string;
        lh_nv: string;
        loai: string;
        ma?: string;
        ma_doi_tac: string;
        ma_doi_tac_ql: string;
        mien_thuong: number;
        nhom: string;
        nv: string;
        phi: number;
        stt: number;
        ten: string;
        thue: number;
        tien: number;
        phi_giam: number;
        thue_giam: number;
      }>;
    }
    interface IDanhSachTaiSan {
      so_id: number;
      so_id_dt: number;
      so_id_ts: number;
      ten: string;
      so_luong: number;
      don_gia: number;
      tong_tien: number;
      nam_sx: number;
      han_sd: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai?: string;
      trang_thai_ten?: string;
    }
    interface IDoiTuong {
      ma?: string;
      ten?: string;
      dchi?: string;
      tinh_thanh?: string;
      phuong_xa?: string;
      latitude?: number;
      longitude?: number;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      trang_thai?: string;
      trang_thai_ten?: string;
      sott?: number;
      stt?: number;
      ten_tinh?: string;
      ten_phuong_xa?: string;
    }
    interface INganSachHoTroNgayApDung {
      ngay_ad?: number | string;
      ma_tinh?: string;
      ma_dvi?: string;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
    }
    interface IDanhSachDonViBHXH {
      ma?: string;
      ten?: string;
      ten_tat?: string;
      ten_e?: string;
      mst?: string;
      dchi?: string;
      dthoai?: string;
      logo?: string;
      stt?: number;
      ma_ct?: string;
      ma_nh?: string;
      ten_nh?: string;
      so_tk?: string;
      ten_tk?: string;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      cap?: number;
      sott?: number;
      trang_thai_ten?: string;
    }
    interface INganSachHoTro {
      ma_sp?: string;
      ngay_ad?: number | string;
      ma_tinh?: string;
      ma_dvi?: string;
      ty_le_nsnn?: number;
      ty_le_nsdp?: number;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
    }
    interface ICauHinhHuongHoaHongNgayApDung {
      ngay_ad?: number | string;
      ma_tvv?: string;
      ma_dvi?: string;
      bt?: number;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
    }
    interface ICauHinhHuongHoaHong {
      bt?: number;
      ma_sp?: string;
      bt_ad?: number;
      ma_tvv?: string;
      ma_dvi?: string;
      ma_sp?: string;
      loai_hd?: string;
      loai_ho_gia_dinh?: string;
      so_thang_tu?: number;
      so_thang_toi?: number;
      tlhh?: number;
      stt?: number;
      trang_thai?: string;
      ngay_tao?: string;
      nguoi_tao?: string;
      ngay_cap_nhat?: string;
      nguoi_cap_nhat?: string;
      loai_hd_ten?: string;
      loai_ho_gia_dinh_ten?: string;
    }
  }
}
